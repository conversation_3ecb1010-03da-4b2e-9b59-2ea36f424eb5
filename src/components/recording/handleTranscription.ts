import RequestHelper from "../../utils/requestHelper";
import { checkGrammar } from "../chat/chatUtils";
import { speakWithSelectedVoice } from "../../utils/speechUtils";
import { Audio } from "expo-av";
interface TranscriptionItem {
  key: number;
  type: "req" | "res" | "loading" | "loadingReq" | "loadingRes";
  text: string;
  isTopic: boolean;
  correctedText?: string;
  _id?: string;
}

const playSound = async (uri) => {
  try {
    const { sound } = await Audio.Sound.createAsync(uri);
    await sound.playAsync();
  } catch (error) {
    console.error("Error playing sound:", error);
  }
};

function delay(ms) {
  return new Promise((resolve) => setTimeout(resolve, ms));
}

export const handleRecordingComplete = async (
  transcriptionResult: TranscriptionItem[],
  setTranscriptionResult: (result: TranscriptionItem[]) => void,
  chatId: string,
  uri: string,
  setTextSubmitted: (submitted: boolean) => void,
  isGrammarCorrect: boolean | null,
  setGrammarErrorSpeechText: (
    input: { text: string; color: string }[] | null
  ) => void,
  mp3RecordingRef: any,
  setShouldRestartAfterGrammarError: (should: boolean) => void,
  setIsSpeaking: (speaking: boolean | null) => void,
  isVoiceAssistantMode: boolean = false
) => {
  const loadingKeyReq = Date.now() + Math.floor(Math.random() * 1000);
  let currentTranscriptionResult = [...transcriptionResult];

  currentTranscriptionResult.push({
    key: loadingKeyReq,
    type: "loadingReq",
    text: "",
    isTopic: false,
  });
  setTranscriptionResult(currentTranscriptionResult);
  // checkGrammar

  try {
    const mp3FormData = prepareTranscriptionRequestData(chatId, uri);
    const result = await sendTranscriptionRequest(mp3FormData);
    const { transcription } = result;

    // Only check grammar if in voice assistant mode
    if (isVoiceAssistantMode) {
      const { correct, corrected } = await checkGrammar(transcription);
      console.log("isGrammarCorrect", correct);
      if (!correct) {
        // Stop recording when grammar is incorrect
        if (mp3RecordingRef?.current?.stopRecording) {
          mp3RecordingRef.current.stopRecording();
        }
        // Set flag to restart recording after speech completes
        // const speechText = `you said: ${transcription}. it's incorrect. you should say: ${corrected}`;
        // setShouldRestartAfterGrammarError(true);
        // setGrammarErrorSpeechText({ text: transcription, color: "red" });
        // await playSound(require("../../../assets/tts/before.mp3"));
        // await delay(5000);
        // await speakWithSelectedVoice(transcription, { rate: 0.5 });
        const delayByLength = transcription.length * 110;
        const highlightedText = highlightDifferences(transcription, corrected);
        setGrammarErrorSpeechText(highlightedText); // Set the speech text to be displayed
        await delay(1500);
        await delay(delayByLength);
        mp3RecordingRef.current.restartRecording();

        // Remove the loading indicator and do not proceed with LLM API call
        const errorResult = transcriptionResult.filter(
          (item: TranscriptionItem) => item.key !== loadingKeyReq
        );
        setTranscriptionResult(errorResult);
        setTextSubmitted(false); // Reset text submitted state
        return;
      }
    } else {
      setTextSubmitted(true);
    }
    // Update the state using the initial state *before* the loading indicator was added
    updateTranscriptionWithResult(
      setTranscriptionResult,
      transcription,
      loadingKeyReq,
      transcriptionResult,
      setTextSubmitted
    );
  } catch (error) {
    // Handle error using the state *before* the loading indicator was added
    handleTranscriptionError(
      error,
      transcriptionResult,
      loadingKeyReq,
      setTranscriptionResult
    );
  } finally {
    // Optional: Cleanup MP3 file
    // const filePathMp3 = RNFS.DocumentDirectoryPath + "/test.mp3";
    // RNFS.unlink(filePathMp3).catch(err => console.log("Error deleting temp mp3:", err));
  }
};

// --- Helper Functions for handleRecordingComplete ---
/**
 * Prepares the FormData for the transcription request.
 */
const prepareTranscriptionRequestData = (
  chatId: string,
  uri: string
): FormData => {
  const fileUri = `file://${uri}`;
  const mp3FormData = new FormData();
  mp3FormData.append("file", {
    uri: fileUri,
    type: "audio/m4a",
    name: "test.mp3",
  } as any); // Cast to 'any' for RN FormData compatibility
  return mp3FormData;
};

/**
 * Sends the transcription request to the backend with retry logic.
 */
const sendTranscriptionRequest = async (
  formData: FormData,
  retries = 3,
  delay = 1000
): Promise<any> => {
  for (let i = 0; i < retries; i++) {
    try {
      // Attempt the request
      const result = await RequestHelper("post", "/transcribe", formData, {
        "Content-Type": "multipart/form-data",
      });
      return result; // Success, return the result
    } catch (error: any) {
      // Check if it's a network error or the last attempt
      // Note: The specific error condition for a "network error" might depend
      // on how RequestHelper throws errors. Adjust the condition if needed.
      const isNetworkError =
        error.message?.toLowerCase().includes("network Error") ||
        error.message?.toLowerCase().includes("timeout"); // Example check

      if (i < retries - 1) {
        console.log(
          `Network error detected. Retrying attempt ${i + 1} of ${retries}...`
        );
        // Wait for the specified delay before retrying
        await new Promise((resolve) => setTimeout(resolve, delay));
      } else {
        // If it's not a network error or it's the last retry, re-throw the error
        console.error(
          `Transcription request failed after ${
            i + 1
          } attempts or due to non-network error.`,
          error
        );
        throw error;
      }
    }
  }
  // This line should theoretically not be reached if retries > 0,
  // but included for completeness and to satisfy TypeScript return type.
  throw new Error("Transcription request failed after all retries.");
};

/**
 * Updates the transcription result state after a successful API call.
 */
const updateTranscriptionWithResult = (
  setTranscriptionResult: (result: TranscriptionItem[]) => void,
  transcription: string,
  loadingKeyReq: number,
  transcriptionResult: TranscriptionItem[] = [],
  setTextSubmitted: (submitted: boolean) => void = () => {} // Default to a no-op function
) => {
  // setTextSubmitted(true);
  // Filter out any previous loading indicators
  let resultWithoutLoading = transcriptionResult.filter(
    (item: TranscriptionItem) => !item.type.includes("loading")
  );
  // Create user transcription (req) message object
  const userMessageObject: TranscriptionItem = {
    key: loadingKeyReq,
    type: "req",
    text: transcription,
    isTopic: false,
  };
  const loadingKey = Date.now() + Math.floor(Math.random() * 1000);
  const loadingObject: TranscriptionItem = {
    key: loadingKey,
    type: "loading",
    text: "",
    isTopic: false,
  };
  setTranscriptionResult([
    ...resultWithoutLoading,
    userMessageObject,
    loadingObject,
  ]);
};

/**
 * Handles errors during the transcription process.
 */
const handleTranscriptionError = (
  error: any,
  transcriptionResult: TranscriptionItem[],
  loadingKeyReq: number, // Pass the specific loading key to remove
  setTranscriptionResult: (result: TranscriptionItem[]) => void
) => {
  console.error("Transcription error:", error);
  // Remove the specific loading indicator ('loadingReq') on error
  const errorResult = transcriptionResult.filter(
    (item: TranscriptionItem) => item.key !== loadingKeyReq
  );
  setTranscriptionResult(errorResult);
};

const highlightDifferences = (
  original: string,
  corrected: string
): { text: string; color: string }[] => {
  const originalWords = original.split(" ");
  const correctedWords = corrected.split(" ");
  const result: { text: string; color: string }[] = [];

  let i = 0; // pointer for originalWords
  let j = 0; // pointer for correctedWords

  while (i < originalWords.length || j < correctedWords.length) {
    if (i < originalWords.length && j < correctedWords.length && originalWords[i] === correctedWords[j]) {
      // Words match, add as default color
      result.push({ text: originalWords[i] + " ", color: "black" });
      i++;
      j++;
    } else {
      // Words don't match or one array is exhausted
      let originalDiff = [];
      let correctedDiff = [];

      // Collect differing words from original
      while (i < originalWords.length && originalWords[i] !== correctedWords[j]) {
        originalDiff.push(originalWords[i]);
        i++;
      }

      // Collect differing words from corrected
      while (j < correctedWords.length && originalWords[i - 1] !== correctedWords[j]) { // Check against the last matched word from original
        correctedDiff.push(correctedWords[j]);
        j++;
      }

      if (originalDiff.length > 0) {
        result.push({ text: originalDiff.join(" ") + " ", color: "red" });
      }
      if (correctedDiff.length > 0) {
        result.push({ text: correctedDiff.join(" ") + " ", color: "green" });
      }
    }
  }
  return result;
};
