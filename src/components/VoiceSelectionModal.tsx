import React, { useState, useEffect } from "react";
import { View, Text, TouchableOpacity, Modal, FlatList, SafeAreaView } from "react-native";
import { getAvailableVoices } from "../utils/voiceUtils";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { Ionicons } from "@expo/vector-icons";
import * as Speech from "expo-speech";

interface Voice {
  id: string;
  name: string;
  language: string;
}

interface VoiceSelectionModalProps {
  visible: boolean;
  onClose: () => void;
  onSelectVoice: (voiceId: string) => void;
  selectedVoice: string | null;
  voices: Voice[];
}

const VoiceSelectionModal = ({
  visible,
  onClose,
  onSelectVoice,
  selectedVoice,
  voices
}: VoiceSelectionModalProps) => {
  const [speechRate, setSpeechRate] = useState<number>(0.8);

  useEffect(() => {
    const loadSpeechRate = async () => {
      try {
        const savedRate = await AsyncStorage.getItem("speechRate");
        if (savedRate) {
          setSpeechRate(parseFloat(savedRate));
        }
      } catch (error) {
        console.error("Error loading speech rate:", error);
      }
    };

    loadSpeechRate();
  }, []);

  const playVoiceSample = (voiceId: string) => {
    Speech.speak("This is a sample voice.", {
      voice: voiceId,
      rate: speechRate,
      pitch: 1,
      language: "en-US",
    });
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      onRequestClose={onClose}
    >
      <SafeAreaView style={{ flex: 1, backgroundColor: "rgba(88, 22, 187, 0.63)" }}>
        <View style={{ padding: 16 }}>
          <View style={{ flexDirection: "row", justifyContent: "space-between", alignItems: "center", marginBottom: 20 }}>
            <Text style={{ fontSize: 20, fontWeight: "bold", color: "white" }}>
              انتخاب صدا
            </Text>
            <TouchableOpacity onPress={onClose}>
              <Ionicons name="close" size={30} color="white" />
            </TouchableOpacity>
          </View>
          
          <FlatList
            data={voices}
            keyExtractor={(item) => item.id}
            renderItem={({ item }) => (
              <View
                style={{
                  padding: 16,
                  backgroundColor: selectedVoice === item.id ? "rgba(255, 215, 0, 0.8)" : "white",
                  borderRadius: 8,
                  marginBottom: 10,
                  flexDirection: "row",
                  justifyContent: "space-between",
                  alignItems: "center",
                }}
              >
                <TouchableOpacity onPress={() => onSelectVoice(item.id)} style={{ flex: 1 }}>
                  <Text
                    style={{
                      fontSize: 16,
                      color: selectedVoice === item.id ? "#4C1D95" : "#000",
                      fontWeight: selectedVoice === item.id ? "bold" : "normal"
                    }}
                  >
                    {item.name} ({item.language})
                  </Text>
                </TouchableOpacity>
                <TouchableOpacity onPress={() => playVoiceSample(item.id)} style={{ padding: 8 }}>
                  <Ionicons name="play-circle" size={24} color="#4C1D95" />
                </TouchableOpacity>
              </View>
            )}
          />
        </View>
      </SafeAreaView>
    </Modal>
  );
};

export default VoiceSelectionModal;