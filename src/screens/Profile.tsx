import React, { useEffect, useState } from "react";
import {
  View,
  StyleSheet,
  Text,
  Image,
  Linking,
  TouchableOpacity,
} from "react-native";
import { useDispatch } from "react-redux";
import { logout, updatePremium } from "../store/slices/authSlice";
import { useAppSelector } from "../store/hooks";
import { useIsFocused } from "@react-navigation/native";
import { StatusBar } from "expo-status-bar";
import SubscriptionButton from "../components/SubscriptionButton"; // Adjust the path as necessary
import { convertToPersianNumber } from "../utils/helpers";
import RequestHelper from "../utils/requestHelper";
import { FontAwesome, MaterialCommunityIcons, Ionicons } from "@expo/vector-icons";
import { getAvailableVoices } from "../utils/voiceUtils";
import AsyncStorage from "@react-native-async-storage/async-storage";
import VoiceSelectionModal from "../components/VoiceSelectionModal";

const App = ({}) => {
  useEffect(() => {
    configureGoogleSignIn();
  }, []);
  const dispatch = useDispatch();
  const { email, name, photo, isPremium, remainingDays } = useAppSelector(
    (state) => state.auth
  );
  const [voices, setVoices] = useState<any[]>([]);
  const [selectedVoice, setSelectedVoice] = useState<string | null>(null);
  const [isVoiceModalVisible, setIsVoiceModalVisible] = useState(false);

  useEffect(() => {
    loadVoices();
  }, []);

  const loadVoices = async () => {
    try {
      const availableVoices = await getAvailableVoices();
      setVoices(availableVoices);
      
      // Get saved voice preference
      const savedVoice = await AsyncStorage.getItem("selectedVoice");
      if (savedVoice) {
        setSelectedVoice(savedVoice);
      } else if (availableVoices.length > 0) {
        // Set first voice as default if none saved
        setSelectedVoice(availableVoices[0].id);
      }
    } catch (error) {
      console.error("Error loading voices:", error);
    }
  };

  const selectVoice = async (voiceId: string) => {
    try {
      await AsyncStorage.setItem("selectedVoice", voiceId);
      setSelectedVoice(voiceId);
      setIsVoiceModalVisible(false);
    } catch (error) {
      console.error("Error saving selected voice:", error);
    }
  };

  const isFocused = useIsFocused();
  useEffect(() => {
    if (isFocused) {
      updatePremiumStatus();
    }
  }, [isFocused]);

  const updatePremiumStatus = async () => {
    const { isPremium, remainingDays } = await RequestHelper("get", "/users");
    dispatch(
      updatePremium({
        isPremium,
        remainingDays,
      })
    );
  };

  const configureGoogleSignIn = async () => {
    try {
    } catch (error) {
      console.error("Error configuring Google Sign-In:", error);
    }
  };

  return (
    <View style={styles.container}>
      {isFocused && <StatusBar style="light" />}

      <View style={styles.profileContainer}>
        <Image style={styles.profilePicture} source={{ uri: photo || undefined }} />
        <Text style={styles.name}>{name}</Text>
        <Text style={styles.jobTitle}>{email}</Text>
      </View>
      <View style={styles.buttonContainer}>
        <Text style={styles.editButton} onPress={() => dispatch(logout())}>
          خروج
        </Text>
      </View>
      {isPremium && remainingDays > 0 ? (
        <View style={styles.premiumContainer}>
          <Text style={styles.premiumText}>
            {convertToPersianNumber(remainingDays.toLocaleString())} روز تا
            پایان اشتراک شما باقی مانده است
          </Text>
        </View>
      ) : (
        <SubscriptionButton />
      )}

      {/* Voice Selection Button */}
      <TouchableOpacity
        onPress={() => setIsVoiceModalVisible(true)}
        style={{
          ...styles.crystalButton,
          bottom: 230,
          flexDirection: "row-reverse",
          alignItems: "center",
          justifyContent: "center",
        }}
      >
        <MaterialCommunityIcons
          name="account-voice"
          size={25}
          color="#035ca8"
          style={{ marginLeft: 8 }}
        />
        <Text style={{ ...styles.editButton, color: "#035ca8" }}>
          انتخاب صدا
        </Text>
      </TouchableOpacity>

      <TouchableOpacity
        onPress={() =>
          Linking.openURL("https://www.instagram.com/speakupai.ir/")
        }
        style={{
          ...styles.crystalButton,
          bottom: 170,
          flexDirection: "row-reverse",
          alignItems: "center",
          justifyContent: "center",
        }}
      >
        <FontAwesome
          name="instagram"
          size={25}
          color="#C13584"
          style={{ marginLeft: 8 }}
        />
        <Text style={{ ...styles.editButton, color: "#035ca8" }}>
          ما را در اینستاگرام دنبال کنید
        </Text>
      </TouchableOpacity>

      <TouchableOpacity
        onPress={() => Linking.openURL("https://t.me/speakup_support")}
        style={{
          ...styles.crystalButton,
          bottom: 110,
          flexDirection: "row-reverse",
          alignItems: "center",
          justifyContent: "center",
        }}
      >
        <FontAwesome
          name="telegram"
          size={24}
          color="#229ED9"
          style={{ marginLeft: 8 }}
        />
        <Text style={{ ...styles.editButton, color: "#035ca8" }}>
          چت با پشتیبانی
        </Text>
      </TouchableOpacity>

      <TouchableOpacity
        onPress={() => Linking.openURL("https://spinteam.ir/privacy-policy")}
        style={{
          ...styles.crystalButton,
          flexDirection: "row-reverse",
          alignItems: "center",
          justifyContent: "center",
        }}
      >
        <MaterialCommunityIcons
          name="shield-check"
          size={25}
          color="#035ca8"
          style={{ marginLeft: 8 }}
        />
        <Text style={{ ...styles.editButton, color: "#035ca8" }}>
          سیاست حریم خصوصی
        </Text>
      </TouchableOpacity>

      <VoiceSelectionModal
        visible={isVoiceModalVisible}
        onClose={() => setIsVoiceModalVisible(false)}
        onSelectVoice={selectVoice}
        selectedVoice={selectedVoice}
        voices={voices}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    flexDirection: "column",
    padding: 16,
    backgroundColor: "rgba(88, 22, 187, 0.63)",
    paddingTop: 50,
  },
  premiumContainer: {
    marginTop: 40,
    padding: 10,
    backgroundColor: "rgba(255, 215, 0, 0.8)",
    borderRadius: 10,
    alignItems: "center",
    justifyContent: "center",
    // width: "70%",
    marginHorizontal: "auto",
  },
  premiumText: {
    color: "#4C1D95",
    fontSize: 16,
    fontFamily: "EstedadRegular",
  },
  profileContainer: {
    alignItems: "center",
    marginTop: 40,
  },
  profilePicture: {
    width: 120,
    height: 120,
    borderRadius: 60,
  },
  name: {
    marginTop: 16,
    fontSize: 24,
    fontWeight: "bold",
    color: "white",
  },
  jobTitle: {
    marginTop: 8,
    fontSize: 16,
    color: "white",
  },
  buttonContainer: {
    marginTop: 13,
    alignItems: "center",
  },
  editButton: {
    color: "white",
    fontSize: 16,
    fontFamily: "EstedadRegular",
    // color: "red",
  },
  crystalButton: {
    position: "absolute",
    bottom: 50,
    left: "22%",
    right: "22%",
    alignItems: "center",
    backgroundColor: "white",
    padding: 10,
    // borderRadius: 8,
    justifyContent: "center",
    // width: "70%",
    // opacity: 0.3,
    borderColor: "#BDBDBD", // Border color
    // shadowColor: "#000",
    // shadowOffset: {
    //   width: 0,
    //   height: 2,
    // },
    // shadowOpacity: 0.3,
    // shadowRadius: 4,
    // elevation: 5, // Add shadow (for Android)
  },
});

export default App;
