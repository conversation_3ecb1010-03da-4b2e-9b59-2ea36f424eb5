{"expo": {"name": "SpeakUp", "slug": "speakup", "plugins": [["expo-splash-screen", {"backgroundColor": "#006FB9", "image": "./assets/splashscreen_image-full.png"}], "@react-native-google-signin/google-signin", "expo-font", "@react-native-firebase/app", "@react-native-firebase/messaging", ["expo-build-properties", {"android": {"compileSdkVersion": 35, "targetSdkVersion": 35, "buildToolsVersion": "35.0.0"}}], "expo-asset"], "version": "1.1.3", "orientation": "portrait", "icon": "./assets/icon-transparent.jpg", "updates": {"fallbackToCacheTimeout": 0}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "bundleIdentifier": "fa.speakup.ai"}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/appIcon.png", "backgroundColor": "#FFFFFF"}, "package": "fa.speakup.ai", "googleServicesFile": "./google-services.json", "targetSdkVersion": 35, "versionCode": 113}, "web": {"favicon": "./assets/favicon.png"}, "extra": {"eas": {"projectId": "22e740c9-f669-4b03-97b8-5171f8b682b2"}, "supportsRTL": false}, "owner": "<PERSON><PERSON><PERSON>"}}