{"name": "speakup", "version": "1.1.3", "scripts": {"start": "npx expo start --dev-client", "android": "npx expo run:android", "ios": "npx expo run:ios", "web": "npx expo start --web", "eject": "npx expo eject", "test": "jest", "eas development": "eas build --platform android --profile development", "eas production": "eas build --platform android --profile production", "pre build": "npx expo prebuild --platform android", "gradlew assembleDebug": "cd android && ./gradlew assembleDebug"}, "dependencies": {"@armata99/react-native-selectable-text": "github:sirramin/react-native-selectable-text", "@cafebazaar/react-native-poolakey": "^3.1.2", "@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-firebase/analytics": "^23.3.0", "@react-native-firebase/app": "^23.3.0", "@react-native-firebase/messaging": "^23.3.0", "@react-native-google-signin/google-signin": "^11.0.0", "@react-navigation/bottom-tabs": "^6.5.11", "@react-navigation/native": "^6.1.9", "@react-navigation/native-stack": "^6.9.17", "@reduxjs/toolkit": "^2.8.2", "@types/react": "~19.0.10", "axios": "^1.6.2", "axios-retry": "^4.5.0", "buffer": "^6.0.3", "expo": "^53.0.22", "expo-av": "~15.1.7", "expo-build-properties": "~0.14.8", "expo-clipboard": "~7.1.5", "expo-dev-client": "~5.2.4", "expo-font": "~13.3.2", "expo-linear-gradient": "~14.1.5", "expo-speech": "~13.1.7", "expo-splash-screen": "~0.30.10", "expo-status-bar": "~2.2.3", "node-wav": "^0.0.2", "react": "19.0.0", "react-native": "0.79.5", "react-native-animated-numbers": "^0.6.3", "react-native-audio-record": "^0.2.2", "react-native-audio-recorder-player": "^3.6.12", "react-native-element-dropdown": "^2.12.1", "react-native-fs": "^2.20.0", "react-native-get-random-values": "^1.11.0", "react-native-loading-dots": "github:sirramin/react-native-loading-dots", "react-native-popover-view": "^5.1.9", "react-native-progress": "^5.0.1", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-svg": "15.11.2", "react-native-toast-message": "^2.2.1", "react-redux": "^9.2.0", "redux-persist": "^6.0.0", "uuid": "^10.0.0", "expo-asset": "~11.1.7"}, "devDependencies": {"@babel/core": "^7.24.0", "@babel/helper-remap-async-to-generator": "^7.25.9", "@react-native-community/cli": "^20.0.1", "@types/uuid": "^10.0.0", "jest": "~29.7.0", "patch-package": "^8.0.0", "postinstall-postinstall": "^2.1.0", "typescript": "~5.8.3"}, "private": true}